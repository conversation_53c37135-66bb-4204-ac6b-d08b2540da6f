document.addEventListener('DOMContentLoaded', function() {
    // Mobile accordion functionality
    const accordionHeaders = document.querySelectorAll('.accordion-header');
    const accordionContents = document.querySelectorAll('.accordion-content');
    
    accordionHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const accordionItem = this.closest('.accordion-item');
            const category = accordionItem.getAttribute('data-category');
            const content = document.getElementById(`${category}-content`);
            const isCurrentlyActive = this.classList.contains('active');
            
            // Close all accordion items
            accordionHeaders.forEach(h => h.classList.remove('active'));
            accordionContents.forEach(c => c.classList.remove('active'));
            
            // If the clicked item wasn't active, open it
            if (!isCurrentlyActive) {
                this.classList.add('active');
                content.classList.add('active');
                
                // Smooth scroll to the opened accordion item
                setTimeout(() => {
                    accordionItem.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest'
                    });
                }, 100);
            }
        });
    });
    
    // Handle window resize to ensure proper display switching
    function handleResize() {
        const isMobile = window.innerWidth <= 767;
        const desktopSection = document.querySelector('.service-categories-grid.desktop-only');
        const mobileSection = document.querySelector('.service-categories-accordion.mobile-only');
        
        if (isMobile) {
            if (desktopSection) desktopSection.style.display = 'none';
            if (mobileSection) mobileSection.style.display = 'block';
        } else {
            if (desktopSection) desktopSection.style.display = 'flex';
            if (mobileSection) mobileSection.style.display = 'none';
        }
    }
    
    // Initial check
    handleResize();
    
    // Listen for window resize
    window.addEventListener('resize', handleResize);
    
    // Ensure smooth transitions for accordion content
    accordionContents.forEach(content => {
        content.addEventListener('transitionend', function(e) {
            if (e.propertyName === 'max-height' && !this.classList.contains('active')) {
                this.style.maxHeight = '0';
            }
        });
    });
});
